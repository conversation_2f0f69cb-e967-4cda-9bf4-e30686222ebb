'use client'
import { ChevronUp, Mail, Phone, MapPin, CreditCard, Shield, Truck, RotateCcw } from 'lucide-react'

// Social Media Icons
const FacebookIcon = () => (
  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
  </svg>
)

const TwitterIcon = () => (
  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
  </svg>
)

const InstagramIcon = () => (
  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
  </svg>
)

const YouTubeIcon = () => (
  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
  </svg>
)

// Payment Method Icons
const PayPalIcon = () => (
  <svg className="h-6 w-6" viewBox="0 0 384 512" fill="none">
    <path d="M111.4 295.9c-3.5 19.2-17.4 108.7-21.5 134-.3 1.8-1 2.5-3 2.5H12.3c-7.6 0-13.1-6.6-12.1-13.9L58.8 46.6c1.5-9.6 10.1-16.9 20-16.9 152.3 0 165.1-3.7 204 11.4 60.1 23.3 65.6 79.5 44 140.3-21.5 62.6-72.5 89.5-140.1 90.3-43.4.7-69.5-7-75.3 24.2zM357.1 152c-1.8-13.4-8.9-27.3-18.9-35.8-11.9-10.7-30.2-17.4-52.4-18.2h-90.5c-12.2 0-22.7 8.8-24.5 20.7l-13.7 86.8c-.9 5.8 3.5 11.1 9.4 11.1h56.5c57.9 0 99.7-6.9 119.2-50.4 9.9-22.3 12.7-52.2 14.9-14.2z" fill="#003087"/>
    <path d="M111.4 295.9c-3.5 19.2-17.4 108.7-21.5 134-.3 1.8-1 2.5-3 2.5H12.3c-7.6 0-13.1-6.6-12.1-13.9L58.8 46.6c1.5-9.6 10.1-16.9 20-16.9 152.3 0 165.1-3.7 204 11.4 60.1 23.3 65.6 79.5 44 140.3-21.5 62.6-72.5 89.5-140.1 90.3-43.4.7-69.5-7-75.3 24.2zM357.1 152c-1.8-13.4-8.9-27.3-18.9-35.8-11.9-10.7-30.2-17.4-52.4-18.2h-90.5c-12.2 0-22.7 8.8-24.5 20.7l-13.7 86.8c-.9 5.8 3.5 11.1 9.4 11.1h56.5c57.9 0 99.7-6.9 119.2-50.4 9.9-22.3 12.7-52.2 14.9-14.2z" fill="#0070ba"/>
    <path d="M357.1 152c-1.8-13.4-8.9-27.3-18.9-35.8-11.9-10.7-30.2-17.4-52.4-18.2h-90.5c-12.2 0-22.7 8.8-24.5 20.7l-13.7 86.8c-.9 5.8 3.5 11.1 9.4 11.1h56.5c57.9 0 99.7-6.9 119.2-50.4 9.9-22.3 12.7-52.2 14.9-14.2z" fill="#009cde"/>
  </svg>
)

const StripeIcon = () => (
  <svg className="h-6 w-6" viewBox="0 0 512 214" fill="none">
    <path d="M35 0h442c19.33 0 35 15.67 35 35v144c0 19.33-15.67 35-35 35H35c-19.33 0-35-15.67-35-35V35C0 15.67 15.67 0 35 0z" fill="#6772e5"/>
    <path d="M218.36 85.73c0-8.2 6.66-13.51 17.48-13.51 15.47 0 35.05 4.67 50.52 13.02V56.32c-16.44-6.66-32.88-9.33-50.52-9.33-41.35 0-69.83 21.6-69.83 57.6 0 56.25 77.49 47.18 77.49 71.42 0 9.33-8.2 15.47-20.73 15.47-17.98 0-40.85-7.46-58.83-17.48v28.92c18.48 8.7 38.18 12.52 58.83 12.52 42.48 0 72.55-21.07 72.55-57.6 0-60.39-77.96-49.18-77.96-72.11z" fill="white"/>
    <path d="M35 0h442c19.33 0 35 15.67 35 35v144c0 19.33-15.67 35-35 35H35c-19.33 0-35-15.67-35-35V35C0 15.67 15.67 0 35 0z" fill="none" stroke="#6772e5" strokeWidth="2"/>
  </svg>
)

const CashIcon = () => (
  <svg className="h-6 w-6" viewBox="0 0 512 512" fill="none">
    <rect x="32" y="80" width="448" height="256" rx="16" ry="16" fill="#2d5016"/>
    <rect x="32" y="80" width="448" height="256" rx="16" ry="16" fill="none" stroke="#1a3009" strokeWidth="2"/>
    <circle cx="256" cy="208" r="64" fill="#4ade80"/>
    <circle cx="256" cy="208" r="64" fill="none" stroke="#16a34a" strokeWidth="2"/>
    <text x="256" y="218" textAnchor="middle" fill="#1a3009" fontSize="32" fontWeight="bold">$</text>
    <circle cx="112" cy="128" r="16" fill="#4ade80"/>
    <circle cx="400" cy="128" r="16" fill="#4ade80"/>
    <circle cx="112" cy="288" r="16" fill="#4ade80"/>
    <circle cx="400" cy="288" r="16" fill="#4ade80"/>
    <rect x="48" y="352" width="416" height="32" fill="#059669"/>
    <text x="256" y="374" textAnchor="middle" fill="white" fontSize="14" fontWeight="bold">CASH ON DELIVERY</text>
  </svg>
)
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { APP_NAME, FREE_SHIPPING_MIN_PRICE, AVAILABLE_PAYMENT_METHODS } from '@/lib/constant'

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className='bg-gradient-to-b from-slate-900 to-black text-white'>
      {/* Back to top button */}
      <div className='w-full'>
        <Button
          variant='ghost'
          className='bg-slate-800 hover:bg-slate-700 w-full rounded-none py-4 transition-colors duration-200'
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        >
          <ChevronUp className='mr-2 h-4 w-4' />
          Back to top
        </Button>
      </div>

      {/* Main footer content */}
      <div className='max-w-7xl mx-auto px-4 py-12'>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>

          {/* Company Info */}
          <div className='space-y-4'>
            <div className='flex items-center space-x-2'>
              <Image
                src='/logo.png'
                alt={APP_NAME}
                width={40}
                height={40}
                className='rounded'
              />
              <h3 className='text-xl font-bold text-white'>{APP_NAME}</h3>
            </div>
            <p className='text-slate-300 text-sm leading-relaxed'>
              Your trusted online shopping destination for quality products at unbeatable prices.
              Shop with confidence and enjoy fast, secure delivery.
            </p>
            <div className='space-y-2 text-sm text-slate-300'>
              <div className='flex items-center space-x-2'>
                <MapPin className='h-4 w-4 text-blue-400' />
                <span>123 Commerce Street, Delhi, India 110001</span>
              </div>
              <div className='flex items-center space-x-2'>
                <Phone className='h-4 w-4 text-green-400' />
                <span>+91 98765 43210</span>
              </div>
              <div className='flex items-center space-x-2'>
                <Mail className='h-4 w-4 text-red-400' />
                <span>support@{APP_NAME.toLowerCase()}.com</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className='space-y-4'>
            <h4 className='text-lg font-semibold text-white border-b border-slate-700 pb-2'>
              Quick Links
            </h4>
            <div className='space-y-2 text-sm'>
              <Link href='/search?tag=todays-deal' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Today's Deals
              </Link>
              <Link href='/search?tag=new-arrival' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                New Arrivals
              </Link>
              <Link href='/search?tag=best-seller' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Best Sellers
              </Link>
              <Link href='/search?tag=featured' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Featured Products
              </Link>
              <Link href='/search?category=Shoes' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Shoes
              </Link>
              <Link href='/search?category=T-Shirts' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                T-Shirts
              </Link>
              <Link href='/search?category=Wrist Watches' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Wrist Watches
              </Link>
            </div>
          </div>

          {/* Customer Service */}
          <div className='space-y-4'>
            <h4 className='text-lg font-semibold text-white border-b border-slate-700 pb-2'>
              Customer Service
            </h4>
            <div className='space-y-2 text-sm'>
              <Link href='/page/help' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Help Center
              </Link>
              <Link href='/page/contact-us' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Contact Us
              </Link>
              <Link href='/page/customer-service' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Customer Service
              </Link>
              <Link href='/account/orders' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Track Your Order
              </Link>
              <Link href='/page/returns-policy' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Returns & Exchanges
              </Link>
              <Link href='/account' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                My Account
              </Link>
              <Link href='/cart' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Shopping Cart
              </Link>
            </div>
          </div>

          {/* About & Policies */}
          <div className='space-y-4'>
            <h4 className='text-lg font-semibold text-white border-b border-slate-700 pb-2'>
              About & Policies
            </h4>
            <div className='space-y-2 text-sm'>
              <Link href='/page/about-us' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                About Us
              </Link>
              <Link href='/page/privacy-policy' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Privacy Policy
              </Link>
              <Link href='/page/conditions-of-use' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Terms of Service
              </Link>
              <Link href='/page/returns-policy' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Return Policy
              </Link>
              <Link href='/page/shipping-info' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Shipping Information
              </Link>
              <Link href='/page/faq' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                FAQ
              </Link>
            </div>
          </div>
        </div>

        <Separator className='my-8 bg-slate-700' />

        {/* Features & Benefits */}
        <div className='grid grid-cols-1 md:grid-cols-4 gap-6 mb-8'>
          <div className='flex items-center space-x-3 text-sm'>
            <Truck className='h-8 w-8 text-blue-400 flex-shrink-0' />
            <div>
              <div className='font-semibold text-white'>Free Shipping</div>
              <div className='text-slate-300'>On orders over ${FREE_SHIPPING_MIN_PRICE}</div>
            </div>
          </div>
          <div className='flex items-center space-x-3 text-sm'>
            <RotateCcw className='h-8 w-8 text-green-400 flex-shrink-0' />
            <div>
              <div className='font-semibold text-white'>Easy Returns</div>
              <div className='text-slate-300'>30-day return policy</div>
            </div>
          </div>
          <div className='flex items-center space-x-3 text-sm'>
            <Shield className='h-8 w-8 text-purple-400 flex-shrink-0' />
            <div>
              <div className='font-semibold text-white'>Secure Payment</div>
              <div className='text-slate-300'>100% secure checkout</div>
            </div>
          </div>
          <div className='flex items-center space-x-3 text-sm'>
            <CreditCard className='h-8 w-8 text-yellow-400 flex-shrink-0' />
            <div>
              <div className='font-semibold text-white'>Multiple Payment</div>
              <div className='text-slate-300'>PayPal, Stripe, COD</div>
            </div>
          </div>
        </div>

        <Separator className='my-8 bg-slate-700' />

        {/* Social Media & Payment Methods */}
        <div className='flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0'>

          {/* Social Media */}
          <div className='flex items-center space-x-4'>
            <span className='text-sm text-slate-300 mr-2'>Follow us:</span>
            <Link href='#' className='text-slate-400 hover:text-blue-500 transition-colors duration-200 p-2 bg-slate-800 rounded-full'>
              <FacebookIcon />
            </Link>
            <Link href='#' className='text-slate-400 hover:text-blue-400 transition-colors duration-200 p-2 bg-slate-800 rounded-full'>
              <TwitterIcon />
            </Link>
            <Link href='#' className='text-slate-400 hover:text-pink-500 transition-colors duration-200 p-2 bg-slate-800 rounded-full'>
              <InstagramIcon />
            </Link>
            <Link href='#' className='text-slate-400 hover:text-red-500 transition-colors duration-200 p-2 bg-slate-800 rounded-full'>
              <YouTubeIcon />
            </Link>
          </div>

          {/* Payment Methods */}
          <div className='flex items-center space-x-4'>
            <span className='text-sm text-slate-300 mr-2'>We accept:</span>
            <div className='flex items-center space-x-3'>
              {AVAILABLE_PAYMENT_METHODS.map((method) => {
                const getPaymentIcon = (methodName: string) => {
                  switch (methodName) {
                    case 'PayPal':
                      return <PayPalIcon />
                    case 'Stripe':
                      return <StripeIcon />
                    case 'Cash On Delivery':
                      return <CashIcon />
                    default:
                      return <CreditCard className='h-6 w-6' />
                  }
                }

                return (
                  <div key={method.name} className='bg-white rounded-lg px-3 py-2 flex items-center space-x-2 shadow-sm border border-gray-200'>
                    <div>
                      {getPaymentIcon(method.name)}
                    </div>
                    <span className='text-xs font-semibold text-slate-700'>{method.name === 'Cash On Delivery' ? 'COD' : method.name}</span>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        <Separator className='my-8 bg-slate-700' />

        {/* Copyright */}
        <div className='text-center text-sm text-slate-400'>
          <p>
            © {currentYear} {APP_NAME}. All rights reserved. |
            <Link href='/page/privacy-policy' className='hover:text-white transition-colors duration-200 ml-1'>
              Privacy Policy
            </Link> |
            <Link href='/page/conditions-of-use' className='hover:text-white transition-colors duration-200 ml-1'>
              Terms of Service
            </Link>
          </p>
          <p className='mt-2'>
            Designed with ❤️ for the best shopping experience
          </p>
        </div>
      </div>
    </footer>
  )
}