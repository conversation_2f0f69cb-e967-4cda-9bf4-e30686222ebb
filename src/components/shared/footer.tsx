'use client'
import { ChevronUp, Mail, Phone, MapPin, CreditCard, Shield, Truck, RotateCcw } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { APP_NAME, FREE_SHIPPING_MIN_PRICE, AVAILABLE_PAYMENT_METHODS } from '@/lib/constant'

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className='bg-gradient-to-b from-slate-900 to-black text-white'>
      {/* Back to top button */}
      <div className='w-full'>
        <Button
          variant='ghost'
          className='bg-slate-800 hover:bg-slate-700 w-full rounded-none py-4 transition-colors duration-200'
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        >
          <ChevronUp className='mr-2 h-4 w-4' />
          Back to top
        </Button>
      </div>

      {/* Main footer content */}
      <div className='max-w-7xl mx-auto px-4 py-12'>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>

          {/* Company Info */}
          <div className='space-y-4'>
            <div className='flex items-center space-x-2'>
              <Image
                src='/logo.png'
                alt={APP_NAME}
                width={40}
                height={40}
                className='rounded'
              />
              <h3 className='text-xl font-bold text-white'>{APP_NAME}</h3>
            </div>
            <p className='text-slate-300 text-sm leading-relaxed'>
              Your trusted online shopping destination for quality products at unbeatable prices.
              Shop with confidence and enjoy fast, secure delivery.
            </p>
            <div className='space-y-2 text-sm text-slate-300'>
              <div className='flex items-center space-x-2'>
                <MapPin className='h-4 w-4 text-blue-400' />
                <span>123 Commerce Street, Delhi, India 110001</span>
              </div>
              <div className='flex items-center space-x-2'>
                <Phone className='h-4 w-4 text-green-400' />
                <span>+91 98765 43210</span>
              </div>
              <div className='flex items-center space-x-2'>
                <Mail className='h-4 w-4 text-red-400' />
                <span>support@{APP_NAME.toLowerCase()}.com</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className='space-y-4'>
            <h4 className='text-lg font-semibold text-white border-b border-slate-700 pb-2'>
              Quick Links
            </h4>
            <div className='space-y-2 text-sm'>
              <Link href='/search?tag=todays-deal' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Today's Deals
              </Link>
              <Link href='/search?tag=new-arrival' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                New Arrivals
              </Link>
              <Link href='/search?tag=best-seller' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Best Sellers
              </Link>
              <Link href='/search?tag=featured' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Featured Products
              </Link>
              <Link href='/search?category=Shoes' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Shoes
              </Link>
              <Link href='/search?category=T-Shirts' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                T-Shirts
              </Link>
              <Link href='/search?category=Wrist Watches' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Wrist Watches
              </Link>
            </div>
          </div>

          {/* Customer Service */}
          <div className='space-y-4'>
            <h4 className='text-lg font-semibold text-white border-b border-slate-700 pb-2'>
              Customer Service
            </h4>
            <div className='space-y-2 text-sm'>
              <Link href='/page/help' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Help Center
              </Link>
              <Link href='/page/contact-us' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Contact Us
              </Link>
              <Link href='/page/customer-service' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Customer Service
              </Link>
              <Link href='/account/orders' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Track Your Order
              </Link>
              <Link href='/page/returns-policy' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Returns & Exchanges
              </Link>
              <Link href='/account' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                My Account
              </Link>
              <Link href='/cart' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Shopping Cart
              </Link>
            </div>
          </div>

          {/* About & Policies */}
          <div className='space-y-4'>
            <h4 className='text-lg font-semibold text-white border-b border-slate-700 pb-2'>
              About & Policies
            </h4>
            <div className='space-y-2 text-sm'>
              <Link href='/page/about-us' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                About Us
              </Link>
              <Link href='/page/privacy-policy' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Privacy Policy
              </Link>
              <Link href='/page/conditions-of-use' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Terms of Service
              </Link>
              <Link href='/page/returns-policy' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Return Policy
              </Link>
              <Link href='/page/shipping-info' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Shipping Information
              </Link>
              <Link href='/page/faq' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                FAQ
              </Link>
            </div>
          </div>
        </div>

        <Separator className='my-8 bg-slate-700' />

        {/* Features & Benefits */}
        <div className='grid grid-cols-1 md:grid-cols-4 gap-6 mb-8'>
          <div className='flex items-center space-x-3 text-sm'>
            <Truck className='h-8 w-8 text-blue-400 flex-shrink-0' />
            <div>
              <div className='font-semibold text-white'>Free Shipping</div>
              <div className='text-slate-300'>On orders over ${FREE_SHIPPING_MIN_PRICE}</div>
            </div>
          </div>
          <div className='flex items-center space-x-3 text-sm'>
            <RotateCcw className='h-8 w-8 text-green-400 flex-shrink-0' />
            <div>
              <div className='font-semibold text-white'>Easy Returns</div>
              <div className='text-slate-300'>30-day return policy</div>
            </div>
          </div>
          <div className='flex items-center space-x-3 text-sm'>
            <Shield className='h-8 w-8 text-purple-400 flex-shrink-0' />
            <div>
              <div className='font-semibold text-white'>Secure Payment</div>
              <div className='text-slate-300'>100% secure checkout</div>
            </div>
          </div>
          <div className='flex items-center space-x-3 text-sm'>
            <CreditCard className='h-8 w-8 text-yellow-400 flex-shrink-0' />
            <div>
              <div className='font-semibold text-white'>Multiple Payment</div>
              <div className='text-slate-300'>PayPal, Stripe, COD</div>
            </div>
          </div>
        </div>

        <Separator className='my-8 bg-slate-700' />

        {/* Social Media & Payment Methods */}
        <div className='flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0'>

          {/* Social Media */}
          <div className='flex items-center space-x-4'>
            <span className='text-sm text-slate-300 mr-2'>Follow us:</span>
            <Link href='#' className='text-slate-400 hover:text-blue-400 transition-colors duration-200 px-3 py-1 bg-slate-800 rounded-md text-sm font-medium'>
              Facebook
            </Link>
            <Link href='#' className='text-slate-400 hover:text-blue-400 transition-colors duration-200 px-3 py-1 bg-slate-800 rounded-md text-sm font-medium'>
              Twitter
            </Link>
            <Link href='#' className='text-slate-400 hover:text-pink-400 transition-colors duration-200 px-3 py-1 bg-slate-800 rounded-md text-sm font-medium'>
              Instagram
            </Link>
            <Link href='#' className='text-slate-400 hover:text-red-400 transition-colors duration-200 px-3 py-1 bg-slate-800 rounded-md text-sm font-medium'>
              YouTube
            </Link>
          </div>

          {/* Payment Methods */}
          <div className='flex items-center space-x-4'>
            <span className='text-sm text-slate-300 mr-2'>We accept:</span>
            <div className='flex items-center space-x-2'>
              {AVAILABLE_PAYMENT_METHODS.map((method) => (
                <div key={method.name} className='bg-white rounded px-2 py-1 text-xs font-semibold text-slate-800'>
                  {method.name}
                </div>
              ))}
            </div>
          </div>
        </div>

        <Separator className='my-8 bg-slate-700' />

        {/* Copyright */}
        <div className='text-center text-sm text-slate-400'>
          <p>
            © {currentYear} {APP_NAME}. All rights reserved. |
            <Link href='/page/privacy-policy' className='hover:text-white transition-colors duration-200 ml-1'>
              Privacy Policy
            </Link> |
            <Link href='/page/conditions-of-use' className='hover:text-white transition-colors duration-200 ml-1'>
              Terms of Service
            </Link>
          </p>
          <p className='mt-2'>
            Designed with ❤️ for the best shopping experience
          </p>
        </div>
      </div>
    </footer>
  )
}