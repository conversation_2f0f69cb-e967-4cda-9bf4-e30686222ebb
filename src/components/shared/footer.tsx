'use client'
import { ChevronUp, Mail, Phone, MapPin, CreditCard, Shield, Truck, RotateCcw } from 'lucide-react'

// Social Media Icons
const FacebookIcon = () => (
  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
  </svg>
)

const TwitterIcon = () => (
  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
  </svg>
)

const InstagramIcon = () => (
  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
  </svg>
)

const YouTubeIcon = () => (
  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 24 24">
    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
  </svg>
)

// Payment Method Icons
const PayPalIcon = () => (
  <svg className="h-6 w-6" viewBox="0 0 24 24" fill="currentColor">
    <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a.641.641 0 0 1-.633-.74L23.696.901C23.778.382 24.226 0 24.75 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106z"/>
  </svg>
)

const StripeIcon = () => (
  <svg className="h-6 w-6" viewBox="0 0 24 24" fill="currentColor">
    <path d="M13.976 9.15c-2.172-.806-3.356-1.426-3.356-2.409 0-.831.683-1.305 1.901-1.305 2.227 0 4.515.858 6.09 1.631l.89-5.494C18.252.274 15.697 0 12.165 0 9.667 0 7.589.654 6.104 1.872 4.56 3.147 3.757 4.992 3.757 7.218c0 4.039 2.467 5.76 6.476 7.219 2.585.92 3.445 1.574 3.445 2.583 0 .98-.84 1.545-2.354 1.545-1.875 0-4.965-.921-6.99-2.109l-.9 5.555C5.175 22.99 8.385 24 11.714 24c2.641 0 4.843-.624 6.328-1.813 1.664-1.305 2.525-3.236 2.525-5.732 0-4.128-2.524-5.851-6.591-7.305z"/>
  </svg>
)

const CashIcon = () => (
  <svg className="h-6 w-6" viewBox="0 0 24 24" fill="currentColor">
    <path d="M2 6a2 2 0 012-2h16a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V6zm2 0v12h16V6H4zm8 2a3 3 0 100 6 3 3 0 000-6zm-1 3a1 1 0 112 0 1 1 0 01-2 0z"/>
    <path d="M6 8a1 1 0 100 2 1 1 0 000-2zm12 0a1 1 0 100 2 1 1 0 000-2zM6 14a1 1 0 100 2 1 1 0 000-2zm12 0a1 1 0 100 2 1 1 0 000-2z"/>
  </svg>
)
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { APP_NAME, FREE_SHIPPING_MIN_PRICE, AVAILABLE_PAYMENT_METHODS } from '@/lib/constant'

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className='bg-gradient-to-b from-slate-900 to-black text-white'>
      {/* Back to top button */}
      <div className='w-full'>
        <Button
          variant='ghost'
          className='bg-slate-800 hover:bg-slate-700 w-full rounded-none py-4 transition-colors duration-200'
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        >
          <ChevronUp className='mr-2 h-4 w-4' />
          Back to top
        </Button>
      </div>

      {/* Main footer content */}
      <div className='max-w-7xl mx-auto px-4 py-12'>
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>

          {/* Company Info */}
          <div className='space-y-4'>
            <div className='flex items-center space-x-2'>
              <Image
                src='/logo.png'
                alt={APP_NAME}
                width={40}
                height={40}
                className='rounded'
              />
              <h3 className='text-xl font-bold text-white'>{APP_NAME}</h3>
            </div>
            <p className='text-slate-300 text-sm leading-relaxed'>
              Your trusted online shopping destination for quality products at unbeatable prices.
              Shop with confidence and enjoy fast, secure delivery.
            </p>
            <div className='space-y-2 text-sm text-slate-300'>
              <div className='flex items-center space-x-2'>
                <MapPin className='h-4 w-4 text-blue-400' />
                <span>123 Commerce Street, Delhi, India 110001</span>
              </div>
              <div className='flex items-center space-x-2'>
                <Phone className='h-4 w-4 text-green-400' />
                <span>+91 98765 43210</span>
              </div>
              <div className='flex items-center space-x-2'>
                <Mail className='h-4 w-4 text-red-400' />
                <span>support@{APP_NAME.toLowerCase()}.com</span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div className='space-y-4'>
            <h4 className='text-lg font-semibold text-white border-b border-slate-700 pb-2'>
              Quick Links
            </h4>
            <div className='space-y-2 text-sm'>
              <Link href='/search?tag=todays-deal' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Today's Deals
              </Link>
              <Link href='/search?tag=new-arrival' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                New Arrivals
              </Link>
              <Link href='/search?tag=best-seller' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Best Sellers
              </Link>
              <Link href='/search?tag=featured' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Featured Products
              </Link>
              <Link href='/search?category=Shoes' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Shoes
              </Link>
              <Link href='/search?category=T-Shirts' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                T-Shirts
              </Link>
              <Link href='/search?category=Wrist Watches' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Wrist Watches
              </Link>
            </div>
          </div>

          {/* Customer Service */}
          <div className='space-y-4'>
            <h4 className='text-lg font-semibold text-white border-b border-slate-700 pb-2'>
              Customer Service
            </h4>
            <div className='space-y-2 text-sm'>
              <Link href='/page/help' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Help Center
              </Link>
              <Link href='/page/contact-us' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Contact Us
              </Link>
              <Link href='/page/customer-service' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Customer Service
              </Link>
              <Link href='/account/orders' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Track Your Order
              </Link>
              <Link href='/page/returns-policy' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Returns & Exchanges
              </Link>
              <Link href='/account' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                My Account
              </Link>
              <Link href='/cart' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Shopping Cart
              </Link>
            </div>
          </div>

          {/* About & Policies */}
          <div className='space-y-4'>
            <h4 className='text-lg font-semibold text-white border-b border-slate-700 pb-2'>
              About & Policies
            </h4>
            <div className='space-y-2 text-sm'>
              <Link href='/page/about-us' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                About Us
              </Link>
              <Link href='/page/privacy-policy' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Privacy Policy
              </Link>
              <Link href='/page/conditions-of-use' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Terms of Service
              </Link>
              <Link href='/page/returns-policy' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Return Policy
              </Link>
              <Link href='/page/shipping-info' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                Shipping Information
              </Link>
              <Link href='/page/faq' className='block text-slate-300 hover:text-white transition-colors duration-200'>
                FAQ
              </Link>
            </div>
          </div>
        </div>

        <Separator className='my-8 bg-slate-700' />

        {/* Features & Benefits */}
        <div className='grid grid-cols-1 md:grid-cols-4 gap-6 mb-8'>
          <div className='flex items-center space-x-3 text-sm'>
            <Truck className='h-8 w-8 text-blue-400 flex-shrink-0' />
            <div>
              <div className='font-semibold text-white'>Free Shipping</div>
              <div className='text-slate-300'>On orders over ${FREE_SHIPPING_MIN_PRICE}</div>
            </div>
          </div>
          <div className='flex items-center space-x-3 text-sm'>
            <RotateCcw className='h-8 w-8 text-green-400 flex-shrink-0' />
            <div>
              <div className='font-semibold text-white'>Easy Returns</div>
              <div className='text-slate-300'>30-day return policy</div>
            </div>
          </div>
          <div className='flex items-center space-x-3 text-sm'>
            <Shield className='h-8 w-8 text-purple-400 flex-shrink-0' />
            <div>
              <div className='font-semibold text-white'>Secure Payment</div>
              <div className='text-slate-300'>100% secure checkout</div>
            </div>
          </div>
          <div className='flex items-center space-x-3 text-sm'>
            <CreditCard className='h-8 w-8 text-yellow-400 flex-shrink-0' />
            <div>
              <div className='font-semibold text-white'>Multiple Payment</div>
              <div className='text-slate-300'>PayPal, Stripe, COD</div>
            </div>
          </div>
        </div>

        <Separator className='my-8 bg-slate-700' />

        {/* Social Media & Payment Methods */}
        <div className='flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0'>

          {/* Social Media */}
          <div className='flex items-center space-x-4'>
            <span className='text-sm text-slate-300 mr-2'>Follow us:</span>
            <Link href='#' className='text-slate-400 hover:text-blue-500 transition-colors duration-200 p-2 bg-slate-800 rounded-full'>
              <FacebookIcon />
            </Link>
            <Link href='#' className='text-slate-400 hover:text-blue-400 transition-colors duration-200 p-2 bg-slate-800 rounded-full'>
              <TwitterIcon />
            </Link>
            <Link href='#' className='text-slate-400 hover:text-pink-500 transition-colors duration-200 p-2 bg-slate-800 rounded-full'>
              <InstagramIcon />
            </Link>
            <Link href='#' className='text-slate-400 hover:text-red-500 transition-colors duration-200 p-2 bg-slate-800 rounded-full'>
              <YouTubeIcon />
            </Link>
          </div>

          {/* Payment Methods */}
          <div className='flex items-center space-x-4'>
            <span className='text-sm text-slate-300 mr-2'>We accept:</span>
            <div className='flex items-center space-x-3'>
              {AVAILABLE_PAYMENT_METHODS.map((method) => {
                const getPaymentIcon = (methodName: string) => {
                  switch (methodName) {
                    case 'PayPal':
                      return <PayPalIcon />
                    case 'Stripe':
                      return <StripeIcon />
                    case 'Cash On Delivery':
                      return <CashIcon />
                    default:
                      return <CreditCard className='h-6 w-6' />
                  }
                }

                return (
                  <div key={method.name} className='bg-white rounded-lg px-3 py-2 flex items-center space-x-2 text-slate-800'>
                    <div className='text-blue-600'>
                      {getPaymentIcon(method.name)}
                    </div>
                    <span className='text-xs font-semibold'>{method.name === 'Cash On Delivery' ? 'COD' : method.name}</span>
                  </div>
                )
              })}
            </div>
          </div>
        </div>

        <Separator className='my-8 bg-slate-700' />

        {/* Copyright */}
        <div className='text-center text-sm text-slate-400'>
          <p>
            © {currentYear} {APP_NAME}. All rights reserved. |
            <Link href='/page/privacy-policy' className='hover:text-white transition-colors duration-200 ml-1'>
              Privacy Policy
            </Link> |
            <Link href='/page/conditions-of-use' className='hover:text-white transition-colors duration-200 ml-1'>
              Terms of Service
            </Link>
          </p>
          <p className='mt-2'>
            Designed with ❤️ for the best shopping experience
          </p>
        </div>
      </div>
    </footer>
  )
}