import * as React from 'react'
import Link from 'next/link'
import { X, ChevronRight, UserCircle, MenuIcon, Store } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Drawer, DrawerClose, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle, DrawerTrigger } from '@/components/ui/drawer'
import { auth } from '@/auth'
import { SignOut } from '@/lib/actions/user-actions'

export default async function Sidebar({ categories }: {
  categories: string[]
}) {
  const session = await auth()

  return (
    <Drawer direction='left'>
      <DrawerTrigger className='flex items-center space-x-2 px-4 py-2 bg-blue-800/50 hover:bg-blue-700/50 rounded-md transition-all duration-200 border border-blue-600 hover:border-blue-500'>
        <MenuIcon className='h-5 w-5 text-white' />
        <span className='text-sm font-medium text-white'>All</span>
      </DrawerTrigger>
      <DrawerContent className='w-[380px] mt-0 top-0 bg-white'>
        <div className='flex flex-col h-full'>
          {/* User Sign In Section */}
          <div className='bg-gradient-to-r from-blue-600 to-blue-700 text-white flex items-center justify-between p-4'>
            <DrawerHeader className='p-0'>
              <DrawerTitle className='flex items-center space-x-3'>
                <div className='bg-white/20 p-2 rounded-full'>
                  <UserCircle className='h-6 w-6 text-white' />
                </div>
                {session ? (
                  <DrawerClose asChild>
                    <Link href='/account' className='hover:text-blue-200 transition-colors duration-200'>
                      <span className='text-lg font-semibold'>
                        Hello, {session.user?.name || 'User'}
                      </span>
                    </Link>
                  </DrawerClose>
                ) : (
                  <DrawerClose asChild>
                    <Link href='/sign-in' className='hover:text-blue-200 transition-colors duration-200'>
                      <span className='text-lg font-semibold'>
                        Hello, Sign In
                      </span>
                    </Link>
                  </DrawerClose>
                )}
              </DrawerTitle>
              <DrawerDescription></DrawerDescription>
            </DrawerHeader>
            <DrawerClose asChild>
              <Button variant='ghost' size='icon' className='text-white hover:bg-white/20 rounded-full'>
                <X className='h-5 w-5' />
                <span className='sr-only'>Close</span>
              </Button>
            </DrawerClose>
          </div>

          {/* Shop By Category */}
          <div className='flex-1 overflow-y-auto bg-slate-50'>
            <div className='p-4 bg-white border-b border-slate-200'>
              <div className='flex items-center space-x-2'>
                <Store className='h-5 w-5 text-blue-600' />
                <h2 className='text-lg font-semibold text-slate-800'>Shop By Department</h2>
              </div>
            </div>
            <nav className='flex flex-col'>
              {categories.map((category) => (
                <DrawerClose asChild key={category}>
                  <Link
                    href={`/search?category=${category}`}
                    className='flex items-center justify-between p-4 hover:bg-blue-50 transition-colors duration-200 border-b border-slate-100 group'
                  >
                    <span className='text-slate-700 group-hover:text-blue-700 font-medium'>{category}</span>
                    <ChevronRight className='h-4 w-4 text-slate-400 group-hover:text-blue-600 transition-colors duration-200' />
                  </Link>
                </DrawerClose>
              ))}
            </nav>
          </div>

          {/* Setting and Help */}
          <div className='border-t border-slate-200 bg-white'>
            <div className='p-4 bg-slate-50 border-b border-slate-200'>
              <h2 className='text-lg font-semibold text-slate-800'>Help & Settings</h2>
            </div>
            <div className='flex flex-col'>
              <DrawerClose asChild>
                <Link href='/account' className='p-4 hover:bg-slate-50 transition-colors duration-200 text-slate-700 hover:text-blue-700 border-b border-slate-100'>
                  Your Account
                </Link>
              </DrawerClose>
              <DrawerClose asChild>
                <Link href='/page/customer-service' className='p-4 hover:bg-slate-50 transition-colors duration-200 text-slate-700 hover:text-blue-700 border-b border-slate-100'>
                  Customer Service
                </Link>
              </DrawerClose>
              {session ? (
                <form action={SignOut} className='w-full'>
                  <Button
                    className='w-full justify-start p-4 text-slate-700 hover:text-red-700 hover:bg-red-50 transition-colors duration-200 rounded-none h-auto font-normal'
                    variant='ghost'
                  >
                    Sign Out
                  </Button>
                </form>
              ) : (
                <DrawerClose asChild>
                  <Link href='/sign-in' className='p-4 hover:bg-blue-50 transition-colors duration-200 text-slate-700 hover:text-blue-700'>
                    Sign In
                  </Link>
                </DrawerClose>
              )}
            </div>
          </div>
        </div>
      </DrawerContent>
    </Drawer>
  )
}