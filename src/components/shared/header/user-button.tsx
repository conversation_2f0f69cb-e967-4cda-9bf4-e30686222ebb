import { auth } from '@/auth'

import { But<PERSON>, buttonVariants } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { SignOut } from '@/lib/actions/user-actions'
import { cn } from '@/lib/utils'
import { ChevronDown, UserCircle } from 'lucide-react'
import Link from 'next/link'

export default async function UserButton() {
  const session = await auth()
  return (
    <div className='flex gap-2 items-center'>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className='flex items-center space-x-2 p-2 hover:bg-slate-800/50 rounded-lg transition-all duration-200 group'>
            <UserCircle className='h-7 w-7 text-white group-hover:text-blue-200 transition-colors duration-200' />
            <div className='hidden sm:flex flex-col text-left'>
              <span className='text-xs text-slate-300 group-hover:text-blue-200 transition-colors duration-200'>
                Hello, {session ? (session.user?.name?.split(' ')[0] || 'User') : 'Sign in'}
              </span>
              <span className='text-sm font-semibold text-white group-hover:text-blue-200 transition-colors duration-200'>
                Account & Orders
              </span>
            </div>
            <ChevronDown className='h-4 w-4 text-slate-300 group-hover:text-blue-200 transition-colors duration-200' />
          </button>
        </DropdownMenuTrigger>
        {session ? (
          <DropdownMenuContent className='w-56' align='end' forceMount>
            <DropdownMenuLabel className='font-normal'>
              <div className='flex flex-col space-y-1'>
                <p className='text-sm font-medium leading-none'>
                  {session.user?.name || 'User'}
                </p>
                <p className='text-xs leading-none text-muted-foreground'>
                  {session.user?.email || 'No email'}
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuGroup>
              <Link className='w-full' href='/account'>
                <DropdownMenuItem>Your account</DropdownMenuItem>
              </Link>
              <Link className='w-full' href='/account/orders'>
                <DropdownMenuItem>Your orders</DropdownMenuItem>
              </Link>

              {session.user?.role === 'Admin' && (
                <Link className='w-full' href='/admin/overview'>
                  <DropdownMenuItem>Admin</DropdownMenuItem>
                </Link>
              )}
            </DropdownMenuGroup>
            <DropdownMenuItem className='p-0 mb-1'>
              <form action={SignOut} className='w-full'>
                <Button
                  className='w-full py-4 px-2 h-4 justify-start'
                  variant='ghost'
                >
                  Sign out
                </Button>
              </form>
            </DropdownMenuItem>
          </DropdownMenuContent>
        ) : (
          <DropdownMenuContent className='w-56' align='end' forceMount>
            <DropdownMenuGroup>
              <DropdownMenuItem>
                <Link
                  className={cn(buttonVariants(), 'w-full')}
                  href='/sign-in'
                >
                  Sign in
                </Link>
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuLabel>
              <div className='font-normal'>
                New Customer? <Link href='/sign-up'>Sign up</Link>
              </div>
            </DropdownMenuLabel>
          </DropdownMenuContent>
        )}
      </DropdownMenu>
    </div>
  )
}