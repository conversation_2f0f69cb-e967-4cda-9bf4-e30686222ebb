'use client'

import { ShoppingCartIcon } from 'lucide-react'
import Link from 'next/link'
import useIsMounted from '@/hooks/use-is-mounted'
import { cn } from '@/lib/utils'
import useCartStore from '@/hooks/use-cart-store'
import useCartSidebar from '@/hooks/use-cart-sidebar'

export default function CartButton() {
  const isMounted = useIsMounted()
  const isCartSidebarOpen = useCartSidebar()
  const {
    cart: { items },
  } = useCartStore()
  const cartItemsCount = items.reduce((a, c) => a + c.quantity, 0)
  return (
    <Link href='/cart' className='relative group'>
      <div className='flex flex-col items-center p-2 hover:bg-slate-800/50 rounded-lg transition-all duration-200'>
        <div className='relative'>
          <ShoppingCartIcon className='h-7 w-7 text-white group-hover:text-blue-200 transition-colors duration-200' />

          {isMounted && cartItemsCount > 0 && (
            <span
              className={cn(
                'absolute -top-2 -right-2 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold rounded-full min-w-[20px] h-5 flex items-center justify-center shadow-lg',
                cartItemsCount >= 10 && 'text-[10px] min-w-[22px]'
              )}
            >
              {cartItemsCount > 99 ? '99+' : cartItemsCount}
            </span>
          )}
        </div>

        <span className='text-xs font-medium text-white group-hover:text-blue-200 transition-colors duration-200 mt-1'>
          Cart
        </span>

        {isCartSidebarOpen && (
          <div className='absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-transparent border-t-white z-10' />
        )}
      </div>
    </Link>
  )
}