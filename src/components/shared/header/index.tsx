import Image from "next/image";
import Link from "next/link";
import Menu from "./menu";
import Search from "./search";
import { menu } from "@/lib/menu";
import Sidebar from "./sidebar";
import { getAllCategory } from "@/lib/actions/product.actions";
import { APP_NAME } from "@/lib/constant";

export default async function Header(){

    const allCategories = await getAllCategory()

    return(
        <header className='bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 text-white shadow-lg border-b border-slate-700'>
            {/* Top Header */}
            <div className='max-w-7xl mx-auto px-4 py-3'>
                <div className='flex items-center justify-between'>
                    {/* Logo Section */}
                    <div className='flex items-center space-x-3'>
                        <Link
                            href='/'
                            className='flex items-center space-x-2 hover:opacity-90 transition-opacity duration-200'
                        >
                            <div className='relative'>
                                <Image
                                    src={'/logo.png'}
                                    width={45}
                                    height={45}
                                    alt={APP_NAME}
                                    className='rounded-lg shadow-sm'
                                />
                            </div>
                            <div className='hidden sm:block'>
                                <h1 className='text-xl font-bold text-white'>{APP_NAME}</h1>
                                <p className='text-xs text-slate-300'>Your Shopping Destination</p>
                            </div>
                        </Link>
                    </div>

                    {/* Search Section - Desktop */}
                    <div className='hidden md:block flex-1 max-w-2xl mx-8'>
                        <Search />
                    </div>

                    {/* User Actions */}
                    <div className='flex items-center space-x-4'>
                        <Menu />
                    </div>
                </div>

                {/* Search Section - Mobile */}
                <div className='md:hidden mt-3'>
                    <Search />
                </div>
            </div>

            {/* Navigation Bar */}
            <div className='bg-gradient-to-r from-blue-900 via-blue-800 to-blue-900 border-t border-blue-700'>
                <div className='max-w-7xl mx-auto px-4'>
                    <div className='flex items-center space-x-1'>
                        <Sidebar categories={allCategories} />
                        <div className='flex items-center space-x-1 overflow-x-auto scrollbar-hide py-2'>
                            {menu.map((menuItem) => (
                                <Link
                                    href={menuItem.href}
                                    key={menuItem.href}
                                    className='whitespace-nowrap px-3 py-2 text-sm font-medium text-white hover:text-blue-200 hover:bg-blue-800/50 rounded-md transition-all duration-200 border border-transparent hover:border-blue-600'
                                >
                                    {menuItem.name}
                                </Link>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </header>
    )
}