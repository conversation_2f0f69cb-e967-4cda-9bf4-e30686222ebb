import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { getAllCategory } from "@/lib/actions/product.actions";
import { SearchIcon } from 'lucide-react'

export default async function Search(){

    const categories = await getAllCategory()

    return(
        <form action='/search' method='GET' className='flex items-stretch h-11 shadow-lg rounded-lg overflow-hidden border border-slate-300'>
            <Select name='category'>
                <SelectTrigger className='w-32 h-full bg-white text-slate-700 border-0 border-r border-slate-300 rounded-none font-medium text-sm'>
                    <SelectValue placeholder="All" />
                </SelectTrigger>
                <SelectContent position='popper' className='bg-white border border-slate-300 shadow-lg'>
                    <SelectItem value='all' className='hover:bg-slate-100'>All Categories</SelectItem>
                    {categories.map((category) => (
                        <SelectItem key={category} value={category} className='hover:bg-slate-100'>
                            {category}
                        </SelectItem>
                    ))}
                </SelectContent>
            </Select>
            <Input
                className='flex-1 h-full bg-white text-slate-900 border-0 rounded-none text-base placeholder:text-slate-500 focus:ring-0 focus:outline-none px-4'
                placeholder="Search for products, brands and more..."
                name='q'
                type='search'
            />
            <button
                type='submit'
                className='bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white h-full px-6 transition-all duration-200 flex items-center justify-center group'
            >
                <SearchIcon className='w-5 h-5 group-hover:scale-110 transition-transform duration-200' />
            </button>
        </form>
    )
}