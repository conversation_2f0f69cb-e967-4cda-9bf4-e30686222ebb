@import "tailwindcss";

@plugin "tailwindcss-animate";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}


@layer base {
  :root {
    --background: hsl(0 0% 100%);
    --foreground: hsl(20 14.3% 4.1%);
    --card: hsl(0 0% 100%);
    --card-foreground: hsl(20 14.3% 4.1%);
    --popover: hsl(0 0% 100%);
    --popover-foreground: hsl(20 14.3% 4.1%);
    --primary: hsl(24.6 95% 53.1%);
    --primary-foreground: hsl(60 9.1% 97.8%);
    --secondary: hsl(60 4.8% 95.9%);
    --secondary-foreground: hsl(24 9.8% 10%);
    --muted: hsl(60 4.8% 95.9%);
    --muted-foreground: hsl(25 5.3% 44.7%);
    --accent: hsl(60 4.8% 95.9%);
    --accent-foreground: hsl(24 9.8% 10%);
    --destructive: hsl(0 84.2% 60.2%);
    --destructive-foreground: hsl(60 9.1% 97.8%);
    --border: hsl(20 5.9% 90%);
    --input: hsl(20 5.9% 90%);
    --ring: hsl(24.6 95% 53.1%);
    --radius: hsl(0.3rem);
    --chart-1: hsl(12 76% 61%);
    --chart-2: hsl(173 58% 39%);
    --chart-3: hsl(197 37% 24%);
    --chart-4: hsl(43 74% 66%);
    --chart-5: hsl(27 87% 67%);
  }

  .dark {
    --background: hsl(20 14.3% 4.1%);
    --foreground: hsl(60 9.1% 97.8%);
    --card: hsl(20 14.3% 4.1%);
    --card-foreground: hsl(60 9.1% 97.8%);
    --popover: hsl(20 14.3% 4.1%);
    --popover-foreground: hsl(60 9.1% 97.8%);
    --primary: hsl(20.5 90.2% 48.2%);
    --primary-foreground: hsl(60 9.1% 97.8%);
    --secondary: hsl(12 6.5% 15.1%);
    --secondary-foreground: hsl(60 9.1% 97.8%);
    --muted: hsl(12 6.5% 15.1%);
    --muted-foreground: hsl(24 5.4% 63.9%);
    --accent: hsl(12 6.5% 15.1%);
    --accent-foreground: hsl(60 9.1% 97.8%);
    --destructive: hsl(0 72.2% 50.6%);
    --destructive-foreground: hsl(60 9.1% 97.8%);
    --border: hsl(12 6.5% 15.1%);
    --input: hsl(12 6.5% 15.1%);
    --ring: hsl(20.5 90.2% 48.2%);
    --chart-1: hsl(220 70% 50%);
    --chart-2: hsl(160 60% 45%);
    --chart-3: hsl(30 80% 55%);
    --chart-4: hsl(280 65% 60%);
    --chart-5: hsl(340 75% 55%);
  }
}


@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}


@layer utilities {
  .header-actions{
    @apply cursor-pointer p-1 border border-transparent hover:border-white rounded-[2px];
  }

  .h1-bold{
    @apply font-bold text-2xl lg:text-3xl
  }
  .highlight-link a,
  a.highlight-link {
    @apply text-sky-700  hover:text-orange-700 hover:underline;
  }
  .flex-between{
    @apply flex justify-between items-center
  }
  .item-button {
    @apply p-3 hover:bg-muted hover:no-underline;
  }

  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
}

.quantity-box button{
  width: 100%;
}